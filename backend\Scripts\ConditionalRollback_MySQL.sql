-- =====================================================
-- CONDITIONAL ROLLBACK SCRIPT (MySQL)
-- Description: Smart rollback that adapts to current database state
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE data_crm;

SELECT '==================================================' AS Status;
SELECT 'CONDITIONAL ROLLBACK STARTING...' AS Status;
SELECT '==================================================' AS Status;

-- =====================================================
-- STEP 1: DISABLE FOREIGN KEY CHECKS
-- =====================================================

SET foreign_key_checks = 0;
SELECT 'Disabled foreign key checks' AS Status;

-- =====================================================
-- STEP 2: CHECK CURRENT STATE AND CREATE BACKUPS
-- =====================================================

SELECT 'Step 2: Checking current state and creating backups...' AS Status;

-- Backup firm_natures table if it exists
DROP TABLE IF EXISTS firm_natures_emergency_backup;
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'firm_natures');
SET @sql = CASE 
    WHEN @table_exists > 0 THEN 'CREATE TABLE firm_natures_emergency_backup AS SELECT * FROM firm_natures'
    ELSE 'SELECT "firm_natures table does not exist - no backup needed" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Backup persons table if it exists
DROP TABLE IF EXISTS persons_emergency_backup;
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'persons');
SET @sql = CASE 
    WHEN @table_exists > 0 THEN 'CREATE TABLE persons_emergency_backup AS SELECT * FROM persons'
    ELSE 'SELECT "persons table does not exist - no backup needed" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT 'Emergency backups created (where applicable)' AS Status;

-- =====================================================
-- STEP 3: HANDLE FIRM_NATURES TABLE
-- =====================================================

SELECT 'Step 3: Handling firm_natures table...' AS Status;

-- If firm_natures exists, rename it to sub_categories
SET @firm_natures_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'firm_natures');
SET @sub_categories_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'sub_categories');

-- Only rename if firm_natures exists and sub_categories doesn't exist
SET @sql = CASE 
    WHEN @firm_natures_exists > 0 AND @sub_categories_exists = 0 THEN 'RENAME TABLE firm_natures TO sub_categories'
    WHEN @firm_natures_exists > 0 AND @sub_categories_exists > 0 THEN 'SELECT "Both firm_natures and sub_categories exist - manual intervention needed" AS Status'
    WHEN @firm_natures_exists = 0 AND @sub_categories_exists > 0 THEN 'SELECT "sub_categories already exists - no rename needed" AS Status'
    ELSE 'SELECT "Neither firm_natures nor sub_categories exist - check your database" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- STEP 4: HANDLE PERSONS TABLE
-- =====================================================

SELECT 'Step 4: Handling persons table...' AS Status;

-- Check if persons table exists and what columns it has
SET @persons_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'persons');
SET @has_firm_nature_id = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'firm_nature_id');
SET @has_sub_category_id = (SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'sub_category_id');

-- If persons table exists with firm_nature_id column, rename it to sub_category_id
SET @sql = CASE 
    WHEN @persons_exists > 0 AND @has_firm_nature_id > 0 AND @has_sub_category_id = 0 THEN 'ALTER TABLE persons CHANGE COLUMN firm_nature_id sub_category_id INT NULL'
    WHEN @persons_exists > 0 AND @has_firm_nature_id = 0 AND @has_sub_category_id > 0 THEN 'SELECT "persons table already has sub_category_id column - no change needed" AS Status'
    WHEN @persons_exists > 0 AND @has_firm_nature_id > 0 AND @has_sub_category_id > 0 THEN 'SELECT "persons table has both columns - manual intervention needed" AS Status'
    WHEN @persons_exists = 0 THEN 'SELECT "persons table does not exist - cannot rename column" AS Status'
    ELSE 'SELECT "persons table exists but has neither firm_nature_id nor sub_category_id" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- STEP 5: REMOVE PARTITIONING IF EXISTS
-- =====================================================

SELECT 'Step 5: Checking and removing partitioning...' AS Status;

-- Check if persons table is partitioned
SET @is_partitioned = (SELECT COUNT(*) FROM information_schema.partitions WHERE table_schema = 'data_crm' AND table_name = 'persons' AND partition_name IS NOT NULL);

-- If partitioned, we need to recreate the table without partitioning
SET @sql = CASE 
    WHEN @is_partitioned > 0 THEN 'SELECT "Persons table is partitioned - will recreate without partitioning" AS Status'
    ELSE 'SELECT "Persons table is not partitioned - no action needed" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If partitioned, recreate the table
-- Note: This is a complex operation, so we'll do it step by step
-- First, create a temporary table with the data
DROP TABLE IF EXISTS persons_temp_for_departion;
SET @sql = CASE 
    WHEN @is_partitioned > 0 THEN 'CREATE TABLE persons_temp_for_departion AS SELECT * FROM persons'
    ELSE 'SELECT "No partitioning removal needed" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Drop the partitioned table and recreate it
SET @sql = CASE 
    WHEN @is_partitioned > 0 THEN 'DROP TABLE persons'
    ELSE 'SELECT "No table drop needed" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Recreate persons table without partitioning (only if it was partitioned)
SET @sql = CASE 
    WHEN @is_partitioned > 0 THEN 
        'CREATE TABLE persons (
            id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            division_id INT NOT NULL,
            category_id INT NOT NULL,
            sub_category_id INT NULL,
            name VARCHAR(255) NOT NULL,
            mobile_number VARCHAR(15) NOT NULL,
            nature INT NOT NULL,
            gender INT NULL,
            alternate_numbers JSON NULL,
            primary_email_id VARCHAR(255) NULL,
            alternate_email_ids JSON NULL,
            website VARCHAR(500) NULL,
            date_of_birth DATETIME NULL,
            is_married BOOLEAN NULL,
            date_of_marriage DATETIME NULL,
            working_state INT NULL,
            district VARCHAR(100) NULL,
            pin_code VARCHAR(10) NULL,
            address_line_1 VARCHAR(500) NULL,
            address_line_2 VARCHAR(500) NULL,
            landmark VARCHAR(255) NULL,
            star_rating INT NULL,
            remarks TEXT NULL,
            firm_name VARCHAR(255) NULL,
            number_of_offices INT NULL,
            number_of_branches INT NULL,
            total_employee_strength INT NULL,
            authorized_person_name VARCHAR(255) NULL,
            authorized_person_email VARCHAR(255) NULL,
            designation VARCHAR(100) NULL,
            marketing_contact VARCHAR(255) NULL,
            marketing_designation VARCHAR(100) NULL,
            place_of_posting VARCHAR(255) NULL,
            department VARCHAR(100) NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_deleted BOOLEAN DEFAULT FALSE,
            deleted_at TIMESTAMP NULL
        )'
    ELSE 'SELECT "No table recreation needed" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Restore data from temporary table
SET @sql = CASE 
    WHEN @is_partitioned > 0 THEN 'INSERT INTO persons SELECT * FROM persons_temp_for_departion'
    ELSE 'SELECT "No data restoration needed" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Drop temporary table
DROP TABLE IF EXISTS persons_temp_for_departion;

-- =====================================================
-- STEP 6: CREATE BASIC INDEXES
-- =====================================================

SELECT 'Step 6: Creating basic indexes...' AS Status;

-- Create indexes on sub_categories table (if it exists)
SET @sub_categories_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'sub_categories');
SET @sql = CASE 
    WHEN @sub_categories_exists > 0 THEN 'CREATE INDEX IF NOT EXISTS IX_sub_categories_category_id ON sub_categories(category_id)'
    ELSE 'SELECT "sub_categories table does not exist - cannot create indexes" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create indexes on persons table (if it exists)
SET @persons_exists = (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'persons');
SET @sql = CASE 
    WHEN @persons_exists > 0 THEN 'CREATE INDEX IF NOT EXISTS IX_persons_sub_category_id ON persons(sub_category_id)'
    ELSE 'SELECT "persons table does not exist - cannot create indexes" AS Status'
END;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- STEP 7: RE-ENABLE FOREIGN KEY CHECKS
-- =====================================================

SET foreign_key_checks = 1;
SELECT 'Re-enabled foreign key checks' AS Status;

-- =====================================================
-- STEP 8: FINAL VERIFICATION
-- =====================================================

SELECT 'Step 8: Final verification...' AS Status;

-- Show current state
SELECT 
    TABLE_NAME as 'Current Tables'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' 
AND table_name IN ('sub_categories', 'persons', 'firm_natures')
ORDER BY TABLE_NAME;

-- Show persons table columns
SELECT 
    COLUMN_NAME as 'Persons Columns'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons'
AND column_name IN ('sub_category_id', 'firm_nature_id')
ORDER BY COLUMN_NAME;

-- =====================================================
-- FINAL STATUS
-- =====================================================

SELECT '==================================================' AS Status;
SELECT 'CONDITIONAL ROLLBACK COMPLETED!' AS Status;
SELECT '==================================================' AS Status;
SELECT 'Check the verification results above to see what was done.' AS Status;
SELECT 'Emergency backup tables were created where applicable.' AS Status;
SELECT 'Test your application and remove backup tables when confident.' AS Status;
