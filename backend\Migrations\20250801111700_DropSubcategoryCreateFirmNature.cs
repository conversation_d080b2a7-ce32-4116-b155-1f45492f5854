using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrmApi.Migrations
{
    /// <inheritdoc />
    public partial class DropSubcategoryCreateFirmNature : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Backup existing data
            migrationBuilder.Sql(@"
                DROP TABLE IF EXISTS subcategories_backup;
                CREATE TABLE subcategories_backup AS 
                SELECT * FROM sub_categories WHERE 1=1;
            ");

            // Step 2: Drop foreign key constraint (if exists)
            migrationBuilder.Sql(@"
                SET @constraint_name = (
                    SELECT CONSTRAINT_NAME 
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'persons' 
                    AND COLUMN_NAME = 'sub_category_id' 
                    AND REFERENCED_TABLE_NAME = 'sub_categories'
                    LIMIT 1
                );
                
                SET @sql = IF(@constraint_name IS NOT NULL, 
                    CONCAT('ALTER TABLE persons DROP FOREIGN KEY ', @constraint_name), 
                    'SELECT ''No foreign key constraint found for sub_category_id'' AS message'
                );
                
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            ");

            // Step 3: Drop index (if exists)
            migrationBuilder.Sql(@"
                SET @index_exists = (
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.STATISTICS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'persons' 
                    AND INDEX_NAME = 'IX_Persons_SubCategoryId'
                );
                
                SET @sql = IF(@index_exists > 0, 
                    'DROP INDEX IX_Persons_SubCategoryId ON persons', 
                    'SELECT ''Index IX_Persons_SubCategoryId does not exist'' AS message'
                );
                
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            ");

            // Step 4: Create firm_natures table
            migrationBuilder.Sql(@"
                DROP TABLE IF EXISTS firm_natures;
                
                CREATE TABLE firm_natures (
                    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    category_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    
                    CONSTRAINT FK_firm_natures_categories_category_id 
                        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
                    CONSTRAINT UQ_firm_natures_category_id_name 
                        UNIQUE (category_id, name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                
                CREATE INDEX IX_firm_natures_category_id_name ON firm_natures(category_id, name);
            ");

            // Step 5: Migrate data from sub_categories to firm_natures
            migrationBuilder.Sql(@"
                SET @table_exists = (
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'sub_categories'
                );
                
                SET @sql = IF(@table_exists > 0, 
                    'INSERT INTO firm_natures (id, category_id, name, created_at, updated_at) 
                     SELECT id, category_id, name, created_at, updated_at FROM sub_categories', 
                    'SELECT ''No sub_categories table found to migrate from'' AS message'
                );
                
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            ");

            // Step 6: Add firm_nature_id column to persons table
            migrationBuilder.Sql(@"
                SET @column_exists = (
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'persons' 
                    AND COLUMN_NAME = 'firm_nature_id'
                );
                
                SET @sql = IF(@column_exists = 0, 
                    'ALTER TABLE persons ADD COLUMN firm_nature_id INT NULL', 
                    'SELECT ''firm_nature_id column already exists'' AS message'
                );
                
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            ");

            // Step 7: Migrate data from sub_category_id to firm_nature_id
            migrationBuilder.Sql(@"
                SET @column_exists = (
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'persons' 
                    AND COLUMN_NAME = 'sub_category_id'
                );
                
                SET @sql = IF(@column_exists > 0, 
                    'UPDATE persons SET firm_nature_id = sub_category_id WHERE sub_category_id IS NOT NULL', 
                    'SELECT ''No sub_category_id column found to migrate from'' AS message'
                );
                
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            ");

            // Step 8: Drop sub_category_id column and sub_categories table
            migrationBuilder.Sql(@"
                SET @column_exists = (
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'persons' 
                    AND COLUMN_NAME = 'sub_category_id'
                );
                
                SET @sql = IF(@column_exists > 0, 
                    'ALTER TABLE persons DROP COLUMN sub_category_id', 
                    'SELECT ''sub_category_id column does not exist'' AS message'
                );
                
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
                
                SET @table_exists = (
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'sub_categories'
                );
                
                SET @sql = IF(@table_exists > 0, 
                    'DROP TABLE sub_categories', 
                    'SELECT ''sub_categories table does not exist'' AS message'
                );
                
                PREPARE stmt FROM @sql;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            ");

            // Step 9: Make firm_nature_id required and add constraints
            migrationBuilder.Sql(@"
                UPDATE persons SET firm_nature_id = 1 WHERE firm_nature_id IS NULL;
                
                ALTER TABLE persons MODIFY COLUMN firm_nature_id INT NOT NULL;
                
                ALTER TABLE persons 
                ADD CONSTRAINT FK_persons_firm_natures_firm_nature_id 
                FOREIGN KEY (firm_nature_id) REFERENCES firm_natures(id) ON DELETE RESTRICT;
                
                CREATE INDEX IX_Persons_FirmNatureId ON persons(firm_nature_id);
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Reverse the migration
            migrationBuilder.Sql(@"
                -- Drop constraints and indexes
                ALTER TABLE persons DROP FOREIGN KEY FK_persons_firm_natures_firm_nature_id;
                DROP INDEX IX_Persons_FirmNatureId ON persons;
                
                -- Recreate sub_categories table
                CREATE TABLE sub_categories (
                    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    category_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    
                    CONSTRAINT FK_sub_categories_categories_category_id 
                        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
                    CONSTRAINT UQ_sub_categories_category_id_name 
                        UNIQUE (category_id, name)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                
                -- Restore data from backup
                INSERT INTO sub_categories (id, category_id, name, created_at, updated_at)
                SELECT id, category_id, name, created_at, updated_at FROM subcategories_backup;
                
                -- Add sub_category_id column back
                ALTER TABLE persons ADD COLUMN sub_category_id INT NULL;
                
                -- Migrate data back
                UPDATE persons SET sub_category_id = firm_nature_id WHERE firm_nature_id IS NOT NULL;
                
                -- Drop firm_nature_id column and firm_natures table
                ALTER TABLE persons DROP COLUMN firm_nature_id;
                DROP TABLE firm_natures;
                
                -- Recreate original constraints
                ALTER TABLE persons 
                ADD CONSTRAINT FK_persons_sub_categories_sub_category_id 
                FOREIGN KEY (sub_category_id) REFERENCES sub_categories(id) ON DELETE SET NULL;
                
                CREATE INDEX IX_Persons_SubCategoryId ON persons(sub_category_id);
            ");
        }
    }
}
