-- =====================================================
-- Migration Script: Drop Subcategory and Create Firm Nature
-- Description: Drop subcategories table and create firm_natures table, update persons table
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-08-01
-- =====================================================

USE data_crm;

PRINT 'Starting migration: Drop Subcategory and Create Firm Nature...';

-- =====================================================
-- STEP 1: BACKUP EXISTING DATA (Optional but recommended)
-- =====================================================

SELECT 'Step 1: Creating backup tables...' AS Status;

-- Backup subcategories data if table exists
DROP TABLE IF EXISTS subcategories_backup;
CREATE TABLE subcategories_backup AS 
SELECT * FROM sub_categories WHERE 1=1;

SELECT CONCAT('✓ Backed up ', ROW_COUNT(), ' subcategory records') AS Status;

-- =====================================================
-- STEP 2: DROP FOREIGN KEY CONSTRAINTS (IF THEY EXIST)
-- =====================================================

SELECT 'Step 2: Dropping foreign key constraints...' AS Status;

-- Drop foreign key constraint from persons to sub_categories (check if exists)
SET @constraint_name = (
    SELECT CONSTRAINT_NAME 
    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE TABLE_SCHEMA = 'data_crm' 
    AND TABLE_NAME = 'persons' 
    AND COLUMN_NAME = 'sub_category_id' 
    AND REFERENCED_TABLE_NAME = 'sub_categories'
    LIMIT 1
);

SET @sql = IF(@constraint_name IS NOT NULL, 
    CONCAT('ALTER TABLE persons DROP FOREIGN KEY ', @constraint_name), 
    'SELECT ''No foreign key constraint found for sub_category_id'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT '✓ Dropped foreign key constraint (if existed)' AS Status;

-- =====================================================
-- STEP 3: DROP INDEXES
-- =====================================================

SELECT 'Step 3: Dropping indexes...' AS Status;

-- Drop index on persons.sub_category_id (check if exists)
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'data_crm' 
    AND TABLE_NAME = 'persons' 
    AND INDEX_NAME = 'IX_Persons_SubCategoryId'
);

SET @sql = IF(@index_exists > 0, 
    'DROP INDEX IX_Persons_SubCategoryId ON persons', 
    'SELECT ''Index IX_Persons_SubCategoryId does not exist'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT '✓ Dropped index on sub_category_id (if existed)' AS Status;

-- =====================================================
-- STEP 4: CREATE FIRM_NATURES TABLE
-- =====================================================

SELECT 'Step 4: Creating firm_natures table...' AS Status;

-- Drop firm_natures table if it exists
DROP TABLE IF EXISTS firm_natures;

-- Create firm_natures table
CREATE TABLE firm_natures (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    updated_at DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    
    CONSTRAINT FK_firm_natures_categories_category_id 
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    CONSTRAINT UQ_firm_natures_category_id_name 
        UNIQUE (category_id, name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create indexes on firm_natures
CREATE INDEX IX_firm_natures_category_id_name ON firm_natures(category_id, name);

SELECT '✓ Created firm_natures table with constraints and indexes' AS Status;

-- =====================================================
-- STEP 5: MIGRATE DATA FROM SUB_CATEGORIES TO FIRM_NATURES
-- =====================================================

SELECT 'Step 5: Migrating data from sub_categories to firm_natures...' AS Status;

-- Insert data from sub_categories to firm_natures (if sub_categories exists)
SET @table_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = 'data_crm' 
    AND TABLE_NAME = 'sub_categories'
);

SET @sql = IF(@table_exists > 0, 
    'INSERT INTO firm_natures (id, category_id, name, created_at, updated_at) 
     SELECT id, category_id, name, created_at, updated_at FROM sub_categories', 
    'SELECT ''No sub_categories table found to migrate from'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT CONCAT('✓ Migrated ', ROW_COUNT(), ' records from sub_categories to firm_natures') AS Status;

-- =====================================================
-- STEP 6: UPDATE PERSONS TABLE
-- =====================================================

SELECT 'Step 6: Updating persons table...' AS Status;

-- Add firm_nature_id column to persons table (if not exists)
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'data_crm' 
    AND TABLE_NAME = 'persons' 
    AND COLUMN_NAME = 'firm_nature_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE persons ADD COLUMN firm_nature_id INT NULL', 
    'SELECT ''firm_nature_id column already exists'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update firm_nature_id with values from sub_category_id (if sub_category_id exists)
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'data_crm' 
    AND TABLE_NAME = 'persons' 
    AND COLUMN_NAME = 'sub_category_id'
);

SET @sql = IF(@column_exists > 0, 
    'UPDATE persons SET firm_nature_id = sub_category_id WHERE sub_category_id IS NOT NULL', 
    'SELECT ''No sub_category_id column found to migrate from'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT CONCAT('✓ Updated ', ROW_COUNT(), ' person records with firm_nature_id') AS Status;

-- =====================================================
-- STEP 7: DROP SUB_CATEGORY_ID COLUMN AND SUB_CATEGORIES TABLE
-- =====================================================

SELECT 'Step 7: Dropping old subcategory structures...' AS Status;

-- Drop sub_category_id column from persons (if exists)
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'data_crm' 
    AND TABLE_NAME = 'persons' 
    AND COLUMN_NAME = 'sub_category_id'
);

SET @sql = IF(@column_exists > 0, 
    'ALTER TABLE persons DROP COLUMN sub_category_id', 
    'SELECT ''sub_category_id column does not exist'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Drop sub_categories table (if exists)
SET @table_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = 'data_crm' 
    AND TABLE_NAME = 'sub_categories'
);

SET @sql = IF(@table_exists > 0, 
    'DROP TABLE sub_categories', 
    'SELECT ''sub_categories table does not exist'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT '✓ Dropped old subcategory structures' AS Status;

-- =====================================================
-- STEP 8: MAKE FIRM_NATURE_ID REQUIRED AND ADD CONSTRAINTS
-- =====================================================

SELECT 'Step 8: Adding constraints to firm_nature_id...' AS Status;

-- Make firm_nature_id NOT NULL (set default value for existing NULL records)
UPDATE persons SET firm_nature_id = 1 WHERE firm_nature_id IS NULL;

-- Alter column to be NOT NULL
ALTER TABLE persons MODIFY COLUMN firm_nature_id INT NOT NULL;

-- Add foreign key constraint
ALTER TABLE persons 
ADD CONSTRAINT FK_persons_firm_natures_firm_nature_id 
FOREIGN KEY (firm_nature_id) REFERENCES firm_natures(id) ON DELETE RESTRICT;

-- Add index on firm_nature_id
CREATE INDEX IX_Persons_FirmNatureId ON persons(firm_nature_id);

SELECT '✓ Added constraints and indexes for firm_nature_id' AS Status;

-- =====================================================
-- STEP 9: VERIFICATION
-- =====================================================

SELECT 'Step 9: Verification...' AS Status;

-- Check firm_natures table
SELECT CONCAT('✓ firm_natures table has ', COUNT(*), ' records') AS Status FROM firm_natures;

-- Check persons with firm_nature_id
SELECT CONCAT('✓ persons table has ', COUNT(*), ' records with firm_nature_id') AS Status 
FROM persons WHERE firm_nature_id IS NOT NULL;

-- Check constraints
SELECT CONCAT('✓ Found ', COUNT(*), ' foreign key constraints on firm_nature_id') AS Status
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'data_crm' 
AND TABLE_NAME = 'persons' 
AND COLUMN_NAME = 'firm_nature_id' 
AND REFERENCED_TABLE_NAME = 'firm_natures';

SELECT '';
SELECT '==================================================' AS Status;
SELECT 'MIGRATION COMPLETED SUCCESSFULLY!' AS Status;
SELECT '==================================================' AS Status;
SELECT '';
SELECT 'Summary of changes:' AS Status;
SELECT '✓ Dropped sub_categories table' AS Status;
SELECT '✓ Created firm_natures table' AS Status;
SELECT '✓ Migrated data from sub_categories to firm_natures' AS Status;
SELECT '✓ Updated persons table to use firm_nature_id' AS Status;
SELECT '✓ Dropped sub_category_id column from persons' AS Status;
SELECT '✓ Added proper constraints and indexes' AS Status;
SELECT '' AS Status;
SELECT 'Next steps:' AS Status;
SELECT '1. Test the application thoroughly' AS Status;
SELECT '2. Update any stored procedures or views if needed' AS Status;
SELECT '3. Remove backup tables when confident migration is successful' AS Status;
