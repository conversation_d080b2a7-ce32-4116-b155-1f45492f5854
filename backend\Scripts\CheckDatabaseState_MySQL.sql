-- =====================================================
-- CHECK DATABASE STATE SCRIPT (MySQL)
-- Description: Check what tables and columns currently exist
-- Database: MySQL
-- Author: CRM System Migration
-- Date: 2025-01-01
-- =====================================================

USE data_crm;

SELECT '==================================================' AS Status;
SELECT 'CHECKING CURRENT DATABASE STATE...' AS Status;
SELECT '==================================================' AS Status;

-- =====================================================
-- SECTION 1: LIST ALL TABLES
-- =====================================================

SELECT 'SECTION 1: ALL TABLES IN DATABASE' AS Status;
SELECT '=====================================' AS Status;

SELECT 
    TABLE_NAME as 'Table Name',
    TABLE_TYPE as 'Type',
    ENGINE as 'Engine',
    TABLE_ROWS as 'Row Count'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'data_crm'
ORDER BY TABLE_NAME;

-- =====================================================
-- SECTION 2: CHECK SPECIFIC TABLES
-- =====================================================

SELECT 'SECTION 2: SPECIFIC TABLE EXISTENCE' AS Status;
SELECT '===================================' AS Status;

-- Check each table individually
SELECT 
    'sub_categories' as 'Table Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'DOES NOT EXIST'
    END AS 'Status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'sub_categories'

UNION ALL

SELECT 
    'subcategories' as 'Table Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'DOES NOT EXIST'
    END AS 'Status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'subcategories'

UNION ALL

SELECT 
    'firm_natures' as 'Table Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'DOES NOT EXIST'
    END AS 'Status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'firm_natures'

UNION ALL

SELECT 
    'persons' as 'Table Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'DOES NOT EXIST'
    END AS 'Status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'persons'

UNION ALL

SELECT 
    'persons_temp' as 'Table Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'DOES NOT EXIST'
    END AS 'Status'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'persons_temp';

-- =====================================================
-- SECTION 3: CHECK PERSONS TABLE COLUMNS
-- =====================================================

SELECT 'SECTION 3: PERSONS TABLE COLUMNS' AS Status;
SELECT '==================================' AS Status;

-- Check if persons table exists first
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'persons table EXISTS - checking columns...'
        ELSE 'persons table DOES NOT EXIST'
    END AS persons_table_status
FROM information_schema.tables 
WHERE table_schema = 'data_crm' AND table_name = 'persons';

-- If persons table exists, show all its columns
SELECT 
    COLUMN_NAME as 'Column Name',
    DATA_TYPE as 'Data Type',
    IS_NULLABLE as 'Nullable',
    COLUMN_DEFAULT as 'Default Value'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons'
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- SECTION 4: CHECK SPECIFIC COLUMNS IN PERSONS
-- =====================================================

SELECT 'SECTION 4: SPECIFIC COLUMN CHECKS' AS Status;
SELECT '===================================' AS Status;

-- Check for different possible column names
SELECT 
    'sub_category_id' as 'Column Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS in persons table'
        ELSE 'DOES NOT EXIST in persons table'
    END AS 'Status'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'sub_category_id'

UNION ALL

SELECT 
    'subcategory_id' as 'Column Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS in persons table'
        ELSE 'DOES NOT EXIST in persons table'
    END AS 'Status'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'subcategory_id'

UNION ALL

SELECT 
    'firm_nature_id' as 'Column Name',
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS in persons table'
        ELSE 'DOES NOT EXIST in persons table'
    END AS 'Status'
FROM information_schema.columns 
WHERE table_schema = 'data_crm' AND table_name = 'persons' AND column_name = 'firm_nature_id';

-- =====================================================
-- SECTION 5: CHECK BACKUP TABLES
-- =====================================================

SELECT 'SECTION 5: BACKUP TABLES' AS Status;
SELECT '=========================' AS Status;

-- Check for any backup tables that might exist
SELECT 
    TABLE_NAME as 'Backup Table Name'
FROM information_schema.tables 
WHERE table_schema = 'data_crm' 
AND (table_name LIKE '%backup%' OR table_name LIKE '%temp%')
ORDER BY TABLE_NAME;

-- =====================================================
-- SECTION 6: CHECK FOREIGN KEY CONSTRAINTS
-- =====================================================

SELECT 'SECTION 6: FOREIGN KEY CONSTRAINTS' AS Status;
SELECT '===================================' AS Status;

-- Show all foreign key constraints
SELECT 
    CONSTRAINT_NAME as 'Constraint Name',
    TABLE_NAME as 'Table',
    COLUMN_NAME as 'Column',
    REFERENCED_TABLE_NAME as 'Referenced Table',
    REFERENCED_COLUMN_NAME as 'Referenced Column'
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'data_crm' 
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;

-- =====================================================
-- SECTION 7: CHECK PARTITIONING
-- =====================================================

SELECT 'SECTION 7: PARTITIONING STATUS' AS Status;
SELECT '===============================' AS Status;

-- Check if persons table is partitioned
SELECT 
    TABLE_NAME as 'Table',
    PARTITION_NAME as 'Partition',
    PARTITION_METHOD as 'Method',
    PARTITION_EXPRESSION as 'Expression'
FROM information_schema.partitions 
WHERE table_schema = 'data_crm' 
AND table_name = 'persons'
AND partition_name IS NOT NULL;

-- =====================================================
-- SECTION 8: RECORD COUNTS
-- =====================================================

SELECT 'SECTION 8: RECORD COUNTS' AS Status;
SELECT '=========================' AS Status;

-- Count records in existing tables
-- This section will only show counts for tables that exist

-- Check persons table count
SELECT 
    'persons' as 'Table Name',
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'data_crm' AND table_name = 'persons') > 0 
        THEN (SELECT COUNT(*) FROM persons)
        ELSE 0
    END AS 'Record Count';

-- =====================================================
-- FINAL SUMMARY
-- =====================================================

SELECT '==================================================' AS Status;
SELECT 'DATABASE STATE CHECK COMPLETED' AS Status;
SELECT '==================================================' AS Status;
SELECT 'Use this information to determine what rollback actions are needed.' AS Status;
